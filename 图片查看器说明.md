# 图片均匀显示器

这个Python脚本可以从多个文件夹中均匀地依次显示PNG图片，支持键盘和鼠标控制。

## 功能特点

1. **均匀分配显示**: 自动计算每个文件夹的图片数量，按比例均匀分配到多个轮次中显示
2. **随机打散**: 每轮内的图片会随机打散，避免固定顺序
3. **多种控制方式**: 支持键盘和鼠标控制
4. **进度显示**: 实时显示当前进度和总数
5. **自适应缩放**: 图片自动缩放以适应窗口大小

## 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装Pillow：

```bash
pip install Pillow
```

## 使用方法

1. 确保脚本在包含 `barcode0812` 文件夹的目录中
2. 运行脚本：

```bash
python image_viewer.py
```

## 控制方式

### 键盘控制
- `→` 或 `D` 键：下一张图片
- `←` 或 `A` 键：上一张图片  
- `ESC` 键：退出程序

### 鼠标控制
- 左键点击图片区域：下一张图片
- 右键点击图片区域：上一张图片

### 界面元素
- 进度条：显示当前浏览进度
- 进度标签：显示 "第 X / 总数 张"
- 文件夹信息：显示当前图片所属文件夹和文件名
- 控制按钮：可点击的上一张/下一张按钮

## 算法说明

### 均匀分配算法

假设有3个文件夹：
- 文件夹A：9张图片
- 文件夹B：6张图片  
- 文件夹C：3张图片

程序会按照以下方式分配：

1. 以最大图片数（9）作为轮次数
2. 计算每个文件夹在每轮的基础分配数
3. 动态调整确保所有图片都能被分配
4. 每轮内随机打散图片顺序

最终可能的分配结果：
- 第1轮：A文件夹1张，B文件夹1张，C文件夹1张（随机顺序）
- 第2轮：A文件夹1张，B文件夹1张，C文件夹1张（随机顺序）
- 第3轮：A文件夹1张，B文件夹1张，C文件夹1张（随机顺序）
- ...以此类推

这样确保了不同文件夹的图片能够均匀穿插显示。

## 目录结构要求

```
项目目录/
├── image_viewer.py
├── requirements.txt
├── 图片查看器说明.md
└── barcode0812/
    ├── 文件夹1/
    │   ├── 图片1.png
    │   ├── 图片2.png
    │   └── ...
    ├── 文件夹2/
    │   ├── 图片1.png
    │   └── ...
    └── ...
```

## 注意事项

1. 只支持PNG格式的图片
2. 图片会自动缩放以适应窗口大小，不会放大只会缩小
3. 如果图片无法加载，会显示错误信息
4. 程序启动时会在控制台输出文件夹和图片的统计信息

## 故障排除

1. **找不到barcode0812目录**: 确保脚本在正确的目录中运行
2. **图片无法显示**: 检查图片文件是否损坏，确保是PNG格式
3. **程序无响应**: 检查是否有大量图片需要加载，请耐心等待

## 系统要求

- Python 3.6+
- Pillow库
- tkinter（通常随Python一起安装）
