# 图片均匀显示器使用示例

## 项目概述

这个项目包含了一个Python脚本，可以从多个文件夹中均匀地依次显示PNG图片。项目提供了两个版本：

1. **GUI版本** (`image_viewer.py`) - 带图形界面的完整版本
2. **命令行版本** (`console_image_viewer.py`) - 适合服务器环境的文本版本

## 核心功能

### 均匀分配算法

假设有以下文件夹结构：
- 文件夹A：9张图片
- 文件夹B：6张图片  
- 文件夹C：3张图片

程序会：
1. 以最大图片数（9）作为轮次数
2. 每轮从各文件夹中按比例取出图片
3. 每轮内的图片随机打散
4. 确保不同文件夹的图片均匀穿插显示

### 实际测试结果

在你的 `barcode0812` 目录中：
- 总共157个文件夹
- 1728张PNG图片
- 最多的文件夹有34张图片
- 分为34轮进行显示

## 文件说明

### 主要脚本

1. **`image_viewer.py`** - GUI版本
   - 使用tkinter创建图形界面
   - 支持鼠标和键盘控制
   - 自动缩放图片适应窗口
   - 显示进度条和详细信息

2. **`console_image_viewer.py`** - 命令行版本
   - 纯文本界面
   - 显示图片路径和文件信息
   - 支持跳转和统计功能

3. **`test_image_viewer.py`** - 测试脚本
   - 验证算法正确性
   - 显示分配统计信息

### 配置文件

- **`requirements.txt`** - Python依赖包
- **`图片查看器说明.md`** - 详细使用说明

## 使用方法

### 1. 安装依赖

```bash
pip install Pillow
```

### 2. 运行GUI版本

```bash
python image_viewer.py
```

**控制方式：**
- `→` 或 `D` 键：下一张图片
- `←` 或 `A` 键：上一张图片  
- `ESC` 键：退出程序
- 鼠标左键：下一张图片
- 鼠标右键：上一张图片

### 3. 运行命令行版本

```bash
python console_image_viewer.py
```

**控制命令：**
- `n` 或回车：下一张图片
- `p`：上一张图片
- `j <数字>`：跳转到指定图片
- `s`：显示统计信息
- `q`：退出程序

### 4. 测试算法

```bash
python test_image_viewer.py
```

## 示例输出

### 命令行版本界面

```
================================================================================
图片均匀显示器 - 命令行版本
================================================================================

进度: 第 10 / 1728 张
[█░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░] 0.6%

文件夹: 895-Z9602-03
文件名: 397123124465.png
路径: barcode0812/895-Z9602-03/397123124465.png

文件大小: 5,008 字节

--------------------------------------------------------------------------------
控制说明:
  n/N/回车 - 下一张
  p/P - 上一张
  j <数字> - 跳转到指定图片
  s - 显示统计信息
  q/Q - 退出
--------------------------------------------------------------------------------
```

### 算法测试输出

```
找到 157 个文件夹:
  371-Z97-01: 34 张图片
  391-Z99-04: 34 张图片
  393-Z99-06: 34 张图片
  ...

统计信息:
  总文件夹数: 157
  总图片数: 1728
  最多图片的文件夹: 371-Z97-01 (34 张)
  最少图片的文件夹: 817-Z9502-06 (1 张)

测试均匀分配算法:
  将按 34 轮进行分配
```

## 技术特点

1. **智能分配算法** - 确保不同文件夹的图片均匀分布
2. **随机打散** - 避免固定的显示顺序
3. **进度跟踪** - 实时显示浏览进度
4. **多种控制方式** - 支持键盘、鼠标、命令行操作
5. **错误处理** - 处理文件不存在等异常情况
6. **跨平台兼容** - 支持Windows、Linux、macOS

## 目录结构要求

```
项目目录/
├── image_viewer.py              # GUI版本
├── console_image_viewer.py      # 命令行版本
├── test_image_viewer.py         # 测试脚本
├── requirements.txt             # 依赖包
├── 图片查看器说明.md            # 详细说明
├── 使用示例.md                  # 本文件
└── barcode0812/                 # 图片目录
    ├── 文件夹1/
    │   ├── 图片1.png
    │   ├── 图片2.png
    │   └── ...
    ├── 文件夹2/
    │   ├── 图片1.png
    │   └── ...
    └── ...
```

## 注意事项

1. 只支持PNG格式的图片
2. 确保 `barcode0812` 目录存在且包含图片文件夹
3. GUI版本需要图形界面环境
4. 命令行版本适合在服务器或无图形界面环境中使用

## 故障排除

1. **找不到barcode0812目录**
   - 确保脚本在正确的目录中运行
   - 检查目录名称是否正确

2. **图片无法显示**
   - 检查图片文件是否损坏
   - 确保是PNG格式

3. **GUI无法启动**
   - 检查是否安装了tkinter
   - 在无图形界面环境中使用命令行版本

4. **依赖包安装失败**
   - 使用 `pip install --no-cache-dir Pillow`
   - 检查网络连接和Python版本
