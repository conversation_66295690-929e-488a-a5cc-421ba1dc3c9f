@echo off
chcp 65001 >nul
echo 测试打包环境
echo ==============

echo 检查Python...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python
    pause
    exit /b 1
)

echo.
echo 检查必要文件...
if not exist "image_viewer.py" (
    echo 错误: 找不到image_viewer.py
    pause
    exit /b 1
)

if not exist "console_image_viewer.py" (
    echo 错误: 找不到console_image_viewer.py
    pause
    exit /b 1
)

echo.
echo 检查依赖包...
python -c "import tkinter; print('tkinter: OK')" 2>nul || echo tkinter: 未安装

python -c "import PIL; print('Pillow: OK')" 2>nul || echo Pillow: 未安装

python -c "import PyInstaller; print('PyInstaller: OK')" 2>nul || echo PyInstaller: 未安装

echo.
echo 环境检查完成！
echo.
echo 如果有未安装的包，请先运行 install_requirements.bat
echo 然后运行 build_exe.bat 进行打包
echo.
pause
