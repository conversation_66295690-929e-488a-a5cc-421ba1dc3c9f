# 多区域图片显示器

这是一个专为barcode文件夹设计的多区域图片显示器，可以同时显示所有区域的图片，并支持单区域浏览模式。

## 功能特点

### 1. 九宫格同时显示
- 自动识别barcode文件夹中的所有区域（91-99）
- 九宫格布局同时显示所有区域的图片
- 每个区域都有独立的进度显示和控制按钮
- 实时显示文件夹名称和文件名信息

### 2. 区域识别规则
程序会根据文件夹名称自动识别区域：
- 文件夹格式：`021WE-Z9101-01`
- 提取规则：第一个"-"后三位中，忽略第一个字母（Z/P/X），取后两位数字
- 例如：`021WE-Z9101-01` → 区域91

### 3. 单区域浏览模式
- 点击任意区域的"◀"或"▶"按钮进入单区域模式
- 其他区域的图片会变成黑色（屏蔽状态）
- 当前区域高亮显示（橙色边框）
- 保持九宫格布局不变

### 4. 图片均匀分配算法
- 每个区域内的图片按文件夹均匀分配
- 多轮次随机打散，确保不同文件夹的图片均匀穿插
- 避免连续显示同一文件夹的图片

## 使用方法

### 启动程序
```bash
# 使用虚拟环境的Python启动
/home/<USER>/WS/dataProcess/.venv/bin/python multi_region_viewer.py

# 或者使用启动脚本（如果已设置）
./run_region_viewer.sh
```

### 操作说明

#### 多区域模式（默认）
- 同时显示所有区域的图片
- 每个区域独立显示当前图片和进度
- 可以看到所有区域的浏览进度

#### 单区域模式
- **进入方式**：点击任意区域的任何地方（标题、图片、信息区域等）
- 进入该区域的单区域浏览模式
- 其他区域变成黑色屏蔽状态
- 当前区域高亮显示（橙色边框）

#### 区域间切换
- **直接切换**：点击任意其他区域的任何地方即可切换
- **无需返回**：不需要先返回多区域模式再选择新区域
- **即时响应**：点击后立即切换到新区域并更新显示

#### 图片浏览
- 在单区域模式下，使用"◀"和"▶"按钮浏览当前区域的图片
- 点击按钮前会自动确保当前区域处于活跃状态

#### 返回多区域模式
- 点击顶部的"返回多区域模式"按钮
- 或按ESC键
- 恢复所有区域的正常显示

### 键盘快捷键
- `ESC`: 返回多区域模式（如果在单区域模式）或退出程序
- `1-9`: 快速切换到对应区域的单区域模式
  - 1 → 区域91
  - 2 → 区域92
  - 以此类推

## 界面说明

### 区域面板组成
每个区域面板包含：
1. **区域标题**：显示"区域 XX"
2. **图片显示区域**：280x180像素的图片显示
3. **控制按钮**：
   - "◀" 上一张按钮
   - 进度显示（如：15/403）
   - "▶" 下一张按钮
4. **文件信息**：显示文件夹名称和文件名

### 状态指示
- **正常状态**：灰色边框，蓝色标题，灰色按钮
- **高亮状态**：橙色边框，橙色标题（单区域模式中的当前区域）
- **屏蔽状态**：红色边框，黑色图片区域，深蓝色按钮（仍可点击切换区域）

### 顶部控制栏
- **标题**：多区域图片显示器
- **模式显示**：
  - "多区域模式"（绿色）
  - "单区域模式 - 区域 XX"（橙色）
- **返回按钮**：返回多区域模式（仅在单区域模式下可用）

## 技术特性

### 图片处理
- 支持PNG格式图片
- 自动缩放保持宽高比
- 不放大图片，只缩小以适应显示区域

### 内存优化
- 按需加载图片
- 及时释放图片内存
- 支持大量图片的流畅浏览

### 布局自适应
- 根据区域数量自动调整网格布局
- 支持2x2、3x2、3x3等多种布局
- 窗口大小可调整

## 区域统计信息

当前识别到的区域：
- 区域 91: 403 张图片
- 区域 92: 305 张图片  
- 区域 93: 128 张图片
- 区域 94: 206 张图片
- 区域 95: 224 张图片
- 区域 96: 107 张图片
- 区域 97: 170 张图片
- 区域 99: 185 张图片

总计：8个区域，1728张图片

## 系统要求

- Python 3.6+
- tkinter（通常随Python安装）
- Pillow库（PIL）
- 足够的内存处理图片显示

## 故障排除

1. **程序无法启动**
   - 检查Python虚拟环境是否激活
   - 确认Pillow库已安装：`pip install Pillow`

2. **图片无法显示**
   - 检查barcode文件夹是否存在
   - 确认文件夹中包含PNG格式图片

3. **区域识别错误**
   - 检查文件夹命名是否符合规则
   - 确认第一个"-"后有至少3个字符

4. **界面响应慢**
   - 可能是图片文件过大，程序会自动缩放
   - 检查系统内存使用情况
