#!/usr/bin/env python
"""
区域分类图片显示器
按照文件夹名称中的区域代码对图片进行分类显示
支持区域切换和单区域浏览模式
"""

import os
import random
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import math
from pathlib import Path
import re

class RegionImageViewer:
    def __init__(self, root_dir="barcode"):
        self.root_dir = root_dir
        self.regions = {}  # 区域 -> 图片列表的映射
        self.current_region = None
        self.current_index = 0
        self.region_mode = False  # 是否处于单区域模式
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("区域分类图片显示器")
        self.root.geometry("1400x900")
        self.root.configure(bg='black')
        
        # 设置窗口居中
        self.center_window()
        
        # 创建界面元素
        self.setup_ui()
        
        # 加载图片列表
        self.load_images_by_region()
        
        # 绑定键盘和鼠标事件
        self.bind_events()
        
        # 显示第一张图片
        if self.regions:
            self.show_image()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def extract_region_from_folder(self, folder_name):
        """从文件夹名称中提取区域代码"""
        # 找到第一个'-'符号
        if '-' not in folder_name:
            return None
        
        # 获取第一个'-'后的部分
        after_dash = folder_name.split('-')[1]
        
        # 检查是否至少有3个字符
        if len(after_dash) < 3:
            return None
        
        # 取前三位
        three_chars = after_dash[:3]
        
        # 检查第一位是否为字母（Z, P, X）
        if three_chars[0] not in ['Z', 'P', 'X']:
            return None
        
        # 取后两位数字
        region_digits = three_chars[1:]
        
        # 验证是否为数字
        if not region_digits.isdigit():
            return None
        
        return region_digits
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg='black')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部区域选择框架
        region_frame = tk.Frame(main_frame, bg='black')
        region_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 区域选择标签
        tk.Label(region_frame, text="区域选择:", font=('Arial', 12, 'bold'), 
                bg='black', fg='white').pack(side=tk.LEFT, padx=(0, 10))
        
        # 区域选择下拉框
        self.region_var = tk.StringVar()
        self.region_combo = ttk.Combobox(region_frame, textvariable=self.region_var, 
                                        font=('Arial', 12), width=15, state='readonly')
        self.region_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.region_combo.bind('<<ComboboxSelected>>', self.on_region_selected)
        
        # 模式切换按钮
        self.mode_button = tk.Button(
            region_frame,
            text="切换到单区域模式",
            command=self.toggle_mode,
            font=('Arial', 12),
            bg='darkblue',
            fg='white',
            relief=tk.RAISED,
            bd=2
        )
        self.mode_button.pack(side=tk.LEFT, padx=(10, 0))
        
        # 信息显示框架
        info_frame = tk.Frame(main_frame, bg='black')
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 进度标签
        self.progress_label = tk.Label(
            info_frame, 
            text="第 0 / 0 张", 
            font=('Arial', 14, 'bold'),
            bg='black', 
            fg='white'
        )
        self.progress_label.pack(side=tk.LEFT)
        
        # 模式标签
        self.mode_label = tk.Label(
            info_frame,
            text="全区域模式",
            font=('Arial', 12, 'bold'),
            bg='black',
            fg='yellow'
        )
        self.mode_label.pack(side=tk.RIGHT)
        
        # 文件夹信息标签
        self.folder_label = tk.Label(
            main_frame, 
            text="文件夹: ", 
            font=('Arial', 12),
            bg='black', 
            fg='lightgray'
        )
        self.folder_label.pack(fill=tk.X, pady=(0, 5))
        
        # 文件名标签
        self.filename_label = tk.Label(
            main_frame, 
            text="文件名: ", 
            font=('Arial', 12),
            bg='black', 
            fg='lightgray'
        )
        self.filename_label.pack(fill=tk.X, pady=(0, 10))
        
        # 进度条
        self.progress_bar = ttk.Progressbar(main_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # 图片显示框架
        self.image_frame = tk.Frame(main_frame, bg='black', relief=tk.SUNKEN, bd=2)
        self.image_frame.pack(fill=tk.BOTH, expand=True)
        
        # 图片标签
        self.image_label = tk.Label(self.image_frame, bg='black')
        self.image_label.pack(expand=True)
        
        # 控制按钮框架
        button_frame = tk.Frame(main_frame, bg='black')
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 上一张按钮
        self.prev_button = tk.Button(
            button_frame, 
            text="◀ 上一张 (←/A)", 
            command=self.prev_image,
            font=('Arial', 12),
            bg='darkgray',
            fg='white',
            relief=tk.RAISED,
            bd=2
        )
        self.prev_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 下一张按钮
        self.next_button = tk.Button(
            button_frame, 
            text="下一张 (→/D) ▶", 
            command=self.next_image,
            font=('Arial', 12),
            bg='darkgray',
            fg='white',
            relief=tk.RAISED,
            bd=2
        )
        self.next_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 统计信息标签
        self.stats_label = tk.Label(
            button_frame,
            text="",
            font=('Arial', 10),
            bg='black',
            fg='lightblue'
        )
        self.stats_label.pack(expand=True)
    
    def load_images_by_region(self):
        """按区域加载图片"""
        if not os.path.exists(self.root_dir):
            print(f"目录 {self.root_dir} 不存在")
            return
        
        # 获取所有文件夹及其区域
        region_folders = {}
        
        for item in os.listdir(self.root_dir):
            folder_path = os.path.join(self.root_dir, item)
            if os.path.isdir(folder_path):
                region = self.extract_region_from_folder(item)
                if region:
                    if region not in region_folders:
                        region_folders[region] = []
                    
                    # 获取该文件夹中的图片
                    images = [f for f in os.listdir(folder_path) 
                             if f.lower().endswith('.png')]
                    
                    if images:
                        for img in sorted(images):
                            img_path = os.path.join(folder_path, img)
                            region_folders[region].append({
                                'path': img_path,
                                'folder': item,
                                'filename': img,
                                'region': region
                            })
        
        # 对每个区域的图片进行均匀分配
        self.regions = {}
        for region, images in region_folders.items():
            self.regions[region] = self.distribute_images_evenly(images)
        
        # 更新区域选择下拉框
        region_list = sorted(self.regions.keys())
        self.region_combo['values'] = ['全部区域'] + [f'区域 {r}' for r in region_list]
        if region_list:
            self.region_combo.set('全部区域')
            self.current_region = None
        
        # 更新统计信息
        self.update_stats()
        
        print(f"找到 {len(self.regions)} 个区域:")
        for region in sorted(self.regions.keys()):
            print(f"  区域 {region}: {len(self.regions[region])} 张图片")

    def distribute_images_evenly(self, images):
        """均匀分配图片（类似原始算法）"""
        if not images:
            return []

        # 按文件夹分组
        folder_groups = {}
        for img in images:
            folder = img['folder']
            if folder not in folder_groups:
                folder_groups[folder] = []
            folder_groups[folder].append(img)

        # 按图片数量排序（从多到少）
        folders = []
        for folder, imgs in folder_groups.items():
            folders.append({
                'name': folder,
                'images': imgs,
                'count': len(imgs)
            })

        folders.sort(key=lambda x: x['count'], reverse=True)

        if not folders:
            return []

        # 计算轮次数（以最多图片的文件夹为准）
        max_images = folders[0]['count']
        rounds = max_images

        # 为每个文件夹计算每轮应该分配的图片数
        for folder in folders:
            folder['per_round'] = max(1, folder['count'] // rounds)
            folder['remaining'] = folder['count']
            folder['current_images'] = folder['images'].copy()
            # 随机打散每个文件夹的图片
            random.shuffle(folder['current_images'])

        # 按轮次分配图片
        distributed_images = []

        for round_num in range(rounds):
            round_images = []

            for folder in folders:
                if folder['remaining'] > 0:
                    # 计算这一轮应该取多少张图片
                    remaining_rounds = rounds - round_num
                    images_this_round = min(
                        folder['per_round'],
                        folder['remaining'],
                        math.ceil(folder['remaining'] / remaining_rounds)
                    )

                    # 取出这一轮的图片
                    for _ in range(images_this_round):
                        if folder['current_images']:
                            img = folder['current_images'].pop(0)
                            round_images.append(img)
                            folder['remaining'] -= 1

            # 随机打散这一轮的图片
            random.shuffle(round_images)
            distributed_images.extend(round_images)

        return distributed_images

    def get_current_image_list(self):
        """获取当前显示的图片列表"""
        if self.region_mode and self.current_region:
            return self.regions.get(self.current_region, [])
        else:
            # 全区域模式：合并所有区域的图片
            all_images = []
            for region in sorted(self.regions.keys()):
                all_images.extend(self.regions[region])
            return all_images

    def update_stats(self):
        """更新统计信息"""
        if self.region_mode and self.current_region:
            region_count = len(self.regions.get(self.current_region, []))
            stats_text = f"区域 {self.current_region}: {region_count} 张图片"
        else:
            total_count = sum(len(imgs) for imgs in self.regions.values())
            region_count = len(self.regions)
            stats_text = f"全部 {region_count} 个区域: {total_count} 张图片"

        self.stats_label.config(text=stats_text)

    def on_region_selected(self, event=None):
        """区域选择事件处理"""
        selected = self.region_var.get()

        if selected == '全部区域':
            self.current_region = None
            self.region_mode = False
            self.mode_label.config(text="全区域模式", fg='yellow')
            self.mode_button.config(text="切换到单区域模式")
        else:
            # 提取区域号
            region = selected.replace('区域 ', '')
            self.current_region = region
            self.region_mode = True
            self.mode_label.config(text=f"单区域模式 - 区域 {region}", fg='orange')
            self.mode_button.config(text="切换到全区域模式")

        # 重置索引并显示图片
        self.current_index = 0
        self.update_stats()
        self.show_image()

    def toggle_mode(self):
        """切换显示模式"""
        if self.region_mode:
            # 切换到全区域模式
            self.region_var.set('全部区域')
            self.on_region_selected()
        else:
            # 切换到单区域模式，选择第一个区域
            if self.regions:
                first_region = sorted(self.regions.keys())[0]
                self.region_var.set(f'区域 {first_region}')
                self.on_region_selected()

    def show_image(self):
        """显示当前图片"""
        image_list = self.get_current_image_list()

        if not image_list:
            self.image_label.configure(image='', text="没有图片可显示")
            self.progress_label.config(text="第 0 / 0 张")
            self.folder_label.config(text="文件夹: ")
            self.filename_label.config(text="文件名: ")
            self.progress_bar['value'] = 0
            return

        # 确保索引在有效范围内
        if self.current_index >= len(image_list):
            self.current_index = 0
        elif self.current_index < 0:
            self.current_index = len(image_list) - 1

        current_image = image_list[self.current_index]

        try:
            # 加载图片
            image = Image.open(current_image['path'])

            # 获取显示区域大小
            self.image_frame.update()
            frame_width = self.image_frame.winfo_width()
            frame_height = self.image_frame.winfo_height()

            # 如果框架还没有渲染完成，使用默认大小
            if frame_width <= 1 or frame_height <= 1:
                frame_width = 800
                frame_height = 600

            # 计算缩放比例，保持宽高比
            img_width, img_height = image.size
            scale_w = frame_width / img_width
            scale_h = frame_height / img_height
            scale = min(scale_w, scale_h, 1.0)  # 不放大，只缩小

            new_width = int(img_width * scale)
            new_height = int(img_height * scale)

            # 调整图片大小
            if scale < 1.0:
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 转换为Tkinter可用的格式
            photo = ImageTk.PhotoImage(image)

            # 显示图片
            self.image_label.configure(image=photo, text='')
            self.image_label.image = photo  # 保持引用

            # 更新进度信息
            self.update_progress(current_image, len(image_list))

        except Exception as e:
            print(f"无法加载图片 {current_image['path']}: {e}")
            # 显示错误信息
            self.image_label.configure(image='', text=f"无法加载图片\n{current_image['filename']}")

    def update_progress(self, current_image, total_count):
        """更新进度信息"""
        # 更新进度标签
        self.progress_label.config(text=f"第 {self.current_index + 1} / {total_count} 张")

        # 更新文件夹和文件名信息
        self.folder_label.config(text=f"文件夹: {current_image['folder']} (区域 {current_image['region']})")
        self.filename_label.config(text=f"文件名: {current_image['filename']}")

        # 更新进度条
        if total_count > 0:
            progress_value = ((self.current_index + 1) / total_count) * 100
            self.progress_bar['value'] = progress_value

    def next_image(self):
        """显示下一张图片"""
        image_list = self.get_current_image_list()
        if not image_list:
            return

        self.current_index = (self.current_index + 1) % len(image_list)
        self.show_image()

    def prev_image(self):
        """显示上一张图片"""
        image_list = self.get_current_image_list()
        if not image_list:
            return

        self.current_index = (self.current_index - 1) % len(image_list)
        self.show_image()

    def bind_events(self):
        """绑定键盘和鼠标事件"""
        # 键盘事件
        self.root.bind('<Key>', self.on_key_press)
        self.root.focus_set()  # 确保窗口可以接收键盘事件

        # 鼠标事件
        self.image_label.bind('<Button-1>', self.on_left_click)
        self.image_label.bind('<Button-3>', self.on_right_click)

        # 窗口大小改变事件
        self.root.bind('<Configure>', self.on_window_resize)

    def on_key_press(self, event):
        """处理键盘按键"""
        key = event.keysym.lower()

        if key in ['right', 'd']:
            self.next_image()
        elif key in ['left', 'a']:
            self.prev_image()
        elif key == 'escape':
            self.root.quit()
        elif key in ['1', '2', '3', '4', '5', '6', '7', '8', '9']:
            # 数字键快速切换区域
            self.quick_switch_region(key)

    def quick_switch_region(self, key):
        """快速切换到指定区域"""
        region_list = sorted(self.regions.keys())
        try:
            index = int(key) - 1
            if 0 <= index < len(region_list):
                region = region_list[index]
                self.region_var.set(f'区域 {region}')
                self.on_region_selected()
        except (ValueError, IndexError):
            pass

    def on_left_click(self, event):
        """处理鼠标左键点击 - 下一张"""
        self.next_image()

    def on_right_click(self, event):
        """处理鼠标右键点击 - 上一张"""
        self.prev_image()

    def on_window_resize(self, event):
        """处理窗口大小改变"""
        # 只在主窗口大小改变时重新显示图片
        if event.widget == self.root:
            self.root.after(100, self.show_image)  # 延迟100ms重新显示

    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    print("区域分类图片显示器启动中...")

    # 检查目录是否存在
    if not os.path.exists("barcode"):
        print("错误: 找不到 barcode 目录")
        print("请确保脚本在包含 barcode 文件夹的目录中运行")
        return

    # 创建并运行图片查看器
    viewer = RegionImageViewer()

    print("\n使用说明:")
    print("- 使用区域选择下拉框切换区域")
    print("- 点击'模式切换'按钮在全区域和单区域模式间切换")
    print("- 键盘控制: ←/A(上一张), →/D(下一张), ESC(退出)")
    print("- 数字键1-9: 快速切换到对应区域")
    print("- 鼠标控制: 左键(下一张), 右键(上一张)")
    print("- 在单区域模式下，只显示选中区域的图片")
    print("- 每个区域内的图片会均匀打散显示")

    viewer.run()

if __name__ == "__main__":
    main()
