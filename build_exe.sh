#!/bin/bash

echo "========================================"
echo "图片均匀显示器 - EXE打包脚本 (Linux版)"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

echo "检测到Python版本:"
python3 --version

echo
echo "步骤1: 安装依赖包..."
echo "----------------------------------------"

# 安装必要的依赖包
echo "正在安装Pillow..."
pip3 install Pillow

echo "正在安装PyInstaller..."
pip3 install pyinstaller

echo
echo "步骤2: 清理旧的构建文件..."
echo "----------------------------------------"

# 清理旧的构建文件
if [ -d "build" ]; then
    echo "删除build目录..."
    rm -rf build
fi

if [ -d "dist" ]; then
    echo "删除dist目录..."
    rm -rf dist
fi

if [ -f "*.spec" ]; then
    echo "删除spec文件..."
    rm -f *.spec
fi

echo
echo "步骤3: 打包GUI版本..."
echo "----------------------------------------"

# 打包GUI版本
echo "正在打包image_viewer.py (GUI版本)..."
python3 -m PyInstaller --onefile --windowed --name="图片查看器-GUI版" image_viewer.py

if [ $? -ne 0 ]; then
    echo "错误: GUI版本打包失败"
    exit 1
fi

echo "GUI版本打包成功！"

echo
echo "步骤4: 打包命令行版本..."
echo "----------------------------------------"

# 打包命令行版本
echo "正在打包console_image_viewer.py (命令行版本)..."
python3 -m PyInstaller --onefile --console --name="图片查看器-命令行版" console_image_viewer.py

if [ $? -ne 0 ]; then
    echo "错误: 命令行版本打包失败"
    exit 1
fi

echo "命令行版本打包成功！"

echo
echo "步骤5: 整理输出文件..."
echo "----------------------------------------"

# 创建输出目录
mkdir -p release

# 复制exe文件到release目录
if [ -f "dist/图片查看器-GUI版" ]; then
    cp "dist/图片查看器-GUI版" "release/"
    echo "已复制: 图片查看器-GUI版"
fi

if [ -f "dist/图片查看器-命令行版" ]; then
    cp "dist/图片查看器-命令行版" "release/"
    echo "已复制: 图片查看器-命令行版"
fi

# 复制说明文件
if [ -f "图片查看器说明.md" ]; then
    cp "图片查看器说明.md" "release/"
    echo "已复制: 图片查看器说明.md"
fi

if [ -f "使用示例.md" ]; then
    cp "使用示例.md" "release/"
    echo "已复制: 使用示例.md"
fi

echo
echo "步骤6: 清理临时文件..."
echo "----------------------------------------"

# 清理构建文件
if [ -d "build" ]; then
    rm -rf build
    echo "已删除build目录"
fi

if [ -d "dist" ]; then
    rm -rf dist
    echo "已删除dist目录"
fi

if [ -f "*.spec" ]; then
    rm -f *.spec
    echo "已删除spec文件"
fi

echo
echo "========================================"
echo "打包完成！"
echo "========================================"
echo
echo "输出文件位置: release/ 目录"
echo
echo "生成的文件:"
[ -f "release/图片查看器-GUI版" ] && echo "  ✓ 图片查看器-GUI版"
[ -f "release/图片查看器-命令行版" ] && echo "  ✓ 图片查看器-命令行版"

echo
echo "注意: 在Linux上生成的是可执行文件，不是.exe文件"
echo "如需生成Windows的.exe文件，请在Windows系统上运行bat脚本"
