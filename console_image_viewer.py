#!/usr/bin/env python3
"""
命令行版图片均匀显示器
实现从多个文件夹中均匀地依次显示PNG图片，支持键盘控制
"""

import os
import random
import math
import sys

class ConsoleImageViewer:
    def __init__(self, root_dir="barcode0812"):
        self.root_dir = root_dir
        self.image_list = []
        self.current_index = 0
        
        # 加载图片列表
        self.load_images()
    
    def load_images(self):
        """加载并均匀分配图片"""
        if not os.path.exists(self.root_dir):
            print(f"目录 {self.root_dir} 不存在")
            return
        
        # 获取所有文件夹及其图片数量
        folders = []
        for item in os.listdir(self.root_dir):
            folder_path = os.path.join(self.root_dir, item)
            if os.path.isdir(folder_path):
                images = [f for f in os.listdir(folder_path) 
                         if f.lower().endswith('.png')]
                if images:
                    folders.append({
                        'name': item,
                        'path': folder_path,
                        'images': sorted(images),
                        'count': len(images)
                    })
        
        if not folders:
            print("没有找到包含PNG图片的文件夹")
            return
        
        # 按图片数量排序（从多到少）
        folders.sort(key=lambda x: x['count'], reverse=True)
        
        print(f"找到 {len(folders)} 个文件夹:")
        for i, folder in enumerate(folders[:5]):  # 只显示前5个
            print(f"  {folder['name']}: {folder['count']} 张图片")
        
        if len(folders) > 5:
            print(f"  ... 还有 {len(folders) - 5} 个文件夹")
        
        # 计算轮次和每轮分配
        total_images = sum(folder['count'] for folder in folders)
        max_images = folders[0]['count']
        
        # 使用最大图片数作为轮次数
        rounds = max_images
        
        # 为每个文件夹计算每轮应该分配的图片数
        for folder in folders:
            folder['per_round'] = max(1, folder['count'] // rounds)
            folder['remaining'] = folder['count']
            folder['current_images'] = folder['images'].copy()
            # 随机打散每个文件夹的图片
            random.shuffle(folder['current_images'])
        
        # 按轮次分配图片
        self.image_list = []
        
        for round_num in range(rounds):
            round_images = []
            
            for folder in folders:
                if folder['remaining'] > 0:
                    # 计算这一轮应该取多少张图片
                    remaining_rounds = rounds - round_num
                    images_this_round = min(
                        folder['per_round'],
                        folder['remaining'],
                        math.ceil(folder['remaining'] / remaining_rounds)
                    )
                    
                    # 取出这一轮的图片
                    for _ in range(images_this_round):
                        if folder['current_images']:
                            img_name = folder['current_images'].pop(0)
                            img_path = os.path.join(folder['path'], img_name)
                            round_images.append({
                                'path': img_path,
                                'folder': folder['name'],
                                'filename': img_name
                            })
                            folder['remaining'] -= 1
            
            # 随机打散这一轮的图片
            random.shuffle(round_images)
            self.image_list.extend(round_images)
        
        print(f"\n总共加载了 {len(self.image_list)} 张图片，分为 {rounds} 轮")
    
    def show_current_image(self):
        """显示当前图片信息"""
        if not self.image_list:
            print("没有图片可显示")
            return
        
        current_image = self.image_list[self.current_index]
        
        # 清屏
        os.system('clear' if os.name == 'posix' else 'cls')
        
        print("=" * 80)
        print("图片均匀显示器 - 命令行版本")
        print("=" * 80)
        print()
        
        # 显示进度信息
        progress = f"第 {self.current_index + 1} / {len(self.image_list)} 张"
        print(f"进度: {progress}")
        
        # 显示进度条
        progress_width = 50
        filled = int((self.current_index + 1) / len(self.image_list) * progress_width)
        bar = "█" * filled + "░" * (progress_width - filled)
        percentage = (self.current_index + 1) / len(self.image_list) * 100
        print(f"[{bar}] {percentage:.1f}%")
        print()
        
        # 显示文件夹信息
        print(f"文件夹: {current_image['folder']}")
        print(f"文件名: {current_image['filename']}")
        print(f"路径: {current_image['path']}")
        print()
        
        # 检查文件是否存在
        if os.path.exists(current_image['path']):
            file_size = os.path.getsize(current_image['path'])
            print(f"文件大小: {file_size:,} 字节")
        else:
            print("⚠️  文件不存在")
        
        print()
        print("-" * 80)
        print("控制说明:")
        print("  n/N/回车 - 下一张")
        print("  p/P - 上一张")
        print("  j <数字> - 跳转到指定图片")
        print("  s - 显示统计信息")
        print("  q/Q - 退出")
        print("-" * 80)
        print()
    
    def show_statistics(self):
        """显示统计信息"""
        if not self.image_list:
            return
        
        print("\n" + "=" * 50)
        print("统计信息")
        print("=" * 50)
        
        # 按文件夹统计
        folder_stats = {}
        for img in self.image_list:
            folder = img['folder']
            if folder not in folder_stats:
                folder_stats[folder] = 0
            folder_stats[folder] += 1
        
        print(f"总图片数: {len(self.image_list)}")
        print(f"文件夹数: {len(folder_stats)}")
        print()
        
        # 显示前10个文件夹的统计
        sorted_folders = sorted(folder_stats.items(), key=lambda x: x[1], reverse=True)
        print("文件夹图片数量排行 (前10名):")
        for i, (folder, count) in enumerate(sorted_folders[:10]):
            print(f"  {i+1:2d}. {folder}: {count} 张")
        
        if len(sorted_folders) > 10:
            print(f"  ... 还有 {len(sorted_folders) - 10} 个文件夹")
        
        print("\n按任意键继续...")
        input()
    
    def next_image(self):
        """显示下一张图片"""
        if not self.image_list:
            return
        
        self.current_index = (self.current_index + 1) % len(self.image_list)
    
    def prev_image(self):
        """显示上一张图片"""
        if not self.image_list:
            return
        
        self.current_index = (self.current_index - 1) % len(self.image_list)
    
    def jump_to_image(self, index):
        """跳转到指定图片"""
        if not self.image_list:
            return
        
        if 1 <= index <= len(self.image_list):
            self.current_index = index - 1
            return True
        return False
    
    def run(self):
        """运行程序"""
        if not self.image_list:
            print("没有图片可显示，程序退出")
            return
        
        print("图片均匀显示器启动成功！")
        print("按任意键开始...")
        input()
        
        while True:
            self.show_current_image()
            
            try:
                command = input("请输入命令: ").strip().lower()
                
                if command in ['q', 'quit']:
                    print("感谢使用图片查看器！")
                    break
                elif command in ['n', 'next', '']:
                    self.next_image()
                elif command in ['p', 'prev']:
                    self.prev_image()
                elif command == 's':
                    self.show_statistics()
                elif command.startswith('j '):
                    try:
                        index = int(command.split()[1])
                        if self.jump_to_image(index):
                            print(f"已跳转到第 {index} 张图片")
                        else:
                            print(f"无效的图片编号: {index}")
                        input("按任意键继续...")
                    except (ValueError, IndexError):
                        print("无效的跳转命令，格式: j <数字>")
                        input("按任意键继续...")
                else:
                    print(f"未知命令: {command}")
                    input("按任意键继续...")
                    
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except EOFError:
                print("\n\n程序结束")
                break

def main():
    """主函数"""
    print("图片均匀显示器 - 命令行版本")
    print("正在初始化...")
    
    # 检查目录是否存在
    if not os.path.exists("barcode0812"):
        print("错误: 找不到 barcode0812 目录")
        print("请确保脚本在包含 barcode0812 文件夹的目录中运行")
        return
    
    # 创建并运行图片查看器
    viewer = ConsoleImageViewer()
    viewer.run()

if __name__ == "__main__":
    main()
