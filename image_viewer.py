#!/usr/bin/env python3
"""
图片均匀显示器
实现从多个文件夹中均匀地依次显示PNG图片，支持键盘和鼠标控制
"""

import os
import random
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import math
from pathlib import Path

class ImageViewer:
    def __init__(self, root_dir="barcode"):
        self.root_dir = root_dir
        self.image_list = []
        self.current_index = 0
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("图片均匀显示器")
        self.root.geometry("1200x800")
        self.root.configure(bg='black')
        
        # 设置窗口居中
        self.center_window()
        
        # 创建界面元素
        self.setup_ui()
        
        # 加载图片列表
        self.load_images()
        
        # 绑定键盘和鼠标事件
        self.bind_events()
        
        # 显示第一张图片
        if self.image_list:
            self.show_image()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = tk.Frame(self.root, bg='black')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 进度信息框架
        info_frame = tk.Frame(main_frame, bg='black')
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 进度标签
        self.progress_label = tk.Label(
            info_frame, 
            text="", 
            font=('Arial', 14, 'bold'),
            fg='white', 
            bg='black'
        )
        self.progress_label.pack(side=tk.LEFT)
        
        # 文件夹信息标签
        self.folder_label = tk.Label(
            info_frame, 
            text="", 
            font=('Arial', 12),
            fg='lightgray', 
            bg='black'
        )
        self.folder_label.pack(side=tk.RIGHT)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(
            main_frame, 
            mode='determinate',
            style='Custom.Horizontal.TProgressbar'
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # 图片显示框架
        self.image_frame = tk.Frame(main_frame, bg='black', relief=tk.SUNKEN, bd=2)
        self.image_frame.pack(fill=tk.BOTH, expand=True)
        
        # 图片标签
        self.image_label = tk.Label(self.image_frame, bg='black')
        self.image_label.pack(expand=True)
        
        # 控制按钮框架
        button_frame = tk.Frame(main_frame, bg='black')
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 上一张按钮
        self.prev_button = tk.Button(
            button_frame, 
            text="◀ 上一张 (←/A)", 
            command=self.prev_image,
            font=('Arial', 12),
            bg='darkgray',
            fg='white',
            relief=tk.RAISED,
            bd=2
        )
        self.prev_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 下一张按钮
        self.next_button = tk.Button(
            button_frame, 
            text="下一张 (→/D) ▶", 
            command=self.next_image,
            font=('Arial', 12),
            bg='darkgray',
            fg='white',
            relief=tk.RAISED,
            bd=2
        )
        self.next_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 帮助信息
        help_label = tk.Label(
            button_frame,
            text="使用方向键 ←→ 或 A/D 键切换图片，鼠标点击图片区域也可切换",
            font=('Arial', 10),
            fg='lightgray',
            bg='black'
        )
        help_label.pack(expand=True)
    
    def load_images(self):
        """加载并均匀分配图片"""
        if not os.path.exists(self.root_dir):
            print(f"目录 {self.root_dir} 不存在")
            return
        
        # 获取所有文件夹及其图片数量
        folders = []
        for item in os.listdir(self.root_dir):
            folder_path = os.path.join(self.root_dir, item)
            if os.path.isdir(folder_path):
                images = [f for f in os.listdir(folder_path) 
                         if f.lower().endswith('.png')]
                if images:
                    folders.append({
                        'name': item,
                        'path': folder_path,
                        'images': sorted(images),
                        'count': len(images)
                    })
        
        if not folders:
            print("没有找到包含PNG图片的文件夹")
            return
        
        # 按图片数量排序（从多到少）
        folders.sort(key=lambda x: x['count'], reverse=True)
        
        print(f"找到 {len(folders)} 个文件夹:")
        for folder in folders:
            print(f"  {folder['name']}: {folder['count']} 张图片")
        
        # 计算轮次和每轮分配
        total_images = sum(folder['count'] for folder in folders)
        max_images = folders[0]['count']
        
        # 使用最大图片数作为轮次数
        rounds = max_images
        
        # 为每个文件夹计算每轮应该分配的图片数
        for folder in folders:
            folder['per_round'] = max(1, folder['count'] // rounds)
            folder['remaining'] = folder['count']
            folder['current_images'] = folder['images'].copy()
            # 随机打散每个文件夹的图片
            random.shuffle(folder['current_images'])
        
        # 按轮次分配图片
        self.image_list = []
        
        for round_num in range(rounds):
            round_images = []
            
            for folder in folders:
                if folder['remaining'] > 0:
                    # 计算这一轮应该取多少张图片
                    remaining_rounds = rounds - round_num
                    images_this_round = min(
                        folder['per_round'],
                        folder['remaining'],
                        math.ceil(folder['remaining'] / remaining_rounds)
                    )
                    
                    # 取出这一轮的图片
                    for _ in range(images_this_round):
                        if folder['current_images']:
                            img_name = folder['current_images'].pop(0)
                            img_path = os.path.join(folder['path'], img_name)
                            round_images.append({
                                'path': img_path,
                                'folder': folder['name'],
                                'filename': img_name
                            })
                            folder['remaining'] -= 1
            
            # 随机打散这一轮的图片
            random.shuffle(round_images)
            self.image_list.extend(round_images)
        
        print(f"\n总共加载了 {len(self.image_list)} 张图片，分为 {rounds} 轮")
        
        # 更新进度条
        if self.image_list:
            self.progress_bar['maximum'] = len(self.image_list)
    
    def show_image(self):
        """显示当前图片"""
        if not self.image_list:
            return
        
        current_image = self.image_list[self.current_index]
        
        try:
            # 加载图片
            image = Image.open(current_image['path'])
            
            # 获取显示区域大小
            self.image_frame.update()
            frame_width = self.image_frame.winfo_width()
            frame_height = self.image_frame.winfo_height()
            
            # 如果框架还没有渲染完成，使用默认大小
            if frame_width <= 1 or frame_height <= 1:
                frame_width = 800
                frame_height = 600
            
            # 计算缩放比例，保持宽高比
            img_width, img_height = image.size
            scale_w = frame_width / img_width
            scale_h = frame_height / img_height
            scale = min(scale_w, scale_h, 1.0)  # 不放大，只缩小
            
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            
            # 调整图片大小
            if scale < 1.0:
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter可用的格式
            photo = ImageTk.PhotoImage(image)
            
            # 显示图片
            self.image_label.configure(image=photo)
            self.image_label.image = photo  # 保持引用
            
            # 更新进度信息
            self.update_progress()
            
        except Exception as e:
            print(f"无法加载图片 {current_image['path']}: {e}")
            # 显示错误信息
            self.image_label.configure(image='', text=f"无法加载图片\n{current_image['filename']}")
    
    def update_progress(self):
        """更新进度显示"""
        if not self.image_list:
            return
        
        current_image = self.image_list[self.current_index]
        
        # 更新进度标签
        progress_text = f"第 {self.current_index + 1} / {len(self.image_list)} 张"
        self.progress_label.configure(text=progress_text)
        
        # 更新文件夹信息
        folder_text = f"文件夹: {current_image['folder']} | 文件: {current_image['filename']}"
        self.folder_label.configure(text=folder_text)
        
        # 更新进度条
        self.progress_bar['value'] = self.current_index + 1
    
    def next_image(self):
        """显示下一张图片"""
        if not self.image_list:
            return
        
        self.current_index = (self.current_index + 1) % len(self.image_list)
        self.show_image()
    
    def prev_image(self):
        """显示上一张图片"""
        if not self.image_list:
            return
        
        self.current_index = (self.current_index - 1) % len(self.image_list)
        self.show_image()
    
    def bind_events(self):
        """绑定键盘和鼠标事件"""
        # 键盘事件
        self.root.bind('<Key>', self.on_key_press)
        self.root.focus_set()  # 确保窗口可以接收键盘事件
        
        # 鼠标事件
        self.image_label.bind('<Button-1>', self.on_left_click)
        self.image_label.bind('<Button-3>', self.on_right_click)
        
        # 窗口大小改变事件
        self.root.bind('<Configure>', self.on_window_resize)
    
    def on_key_press(self, event):
        """处理键盘按键"""
        key = event.keysym.lower()
        
        if key in ['right', 'd']:
            self.next_image()
        elif key in ['left', 'a']:
            self.prev_image()
        elif key == 'escape':
            self.root.quit()
    
    def on_left_click(self, event):
        """处理鼠标左键点击"""
        self.next_image()
    
    def on_right_click(self, event):
        """处理鼠标右键点击"""
        self.prev_image()
    
    def on_window_resize(self, event):
        """处理窗口大小改变"""
        # 只在主窗口大小改变时重新显示图片
        if event.widget == self.root:
            self.root.after(100, self.show_image)  # 延迟100ms重新显示
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    print("图片均匀显示器启动中...")
    
    # 检查目录是否存在
    if not os.path.exists("barcode"):
        print("错误: 找不到 barcode 目录")
        print("请确保脚本在包含 barcode 文件夹的目录中运行")
        return
    
    # 创建并运行图片查看器
    viewer = ImageViewer()
    viewer.run()

if __name__ == "__main__":
    main()
