#!/bin/bash
# 多区域图片显示器启动脚本

echo "启动多区域图片显示器..."
echo "选择启动模式:"
echo "1. 多区域同时显示模式 (推荐)"
echo "2. 单区域切换模式"
echo ""
read -p "请输入选择 (1 或 2): " choice

case $choice in
    1)
        echo "启动多区域同时显示模式..."
        /home/<USER>/WS/dataProcess/.venv/bin/python multi_region_viewer.py
        ;;
    2)
        echo "启动单区域切换模式..."
        /home/<USER>/WS/dataProcess/.venv/bin/python region_image_viewer.py
        ;;
    *)
        echo "无效选择，启动默认的多区域模式..."
        /home/<USER>/WS/dataProcess/.venv/bin/python multi_region_viewer.py
        ;;
esac
