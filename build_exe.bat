@echo off
chcp 65001 >nul
echo ========================================
echo 图片均匀显示器 - EXE打包脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo 检测到Python版本:
python --version

echo.
echo 步骤1: 安装依赖包...
echo ----------------------------------------

:: 安装必要的依赖包
echo 正在安装Pillow...
pip install Pillow
if errorlevel 1 (
    echo 警告: Pillow安装失败，但继续执行...
)

echo 正在安装PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo 错误: PyInstaller安装失败
    pause
    exit /b 1
)

echo.
echo 步骤2: 清理旧的构建文件...
echo ----------------------------------------

:: 清理旧的构建文件
if exist "build" (
    echo 删除build目录...
    rmdir /s /q "build"
)

if exist "dist" (
    echo 删除dist目录...
    rmdir /s /q "dist"
)

if exist "*.spec" (
    echo 删除spec文件...
    del /q "*.spec"
)

echo.
echo 步骤3: 打包GUI版本...
echo ----------------------------------------

:: 打包GUI版本
echo 正在打包image_viewer.py (GUI版本)...
pyinstaller --onefile --windowed --name="图片查看器-GUI版" --icon=icon.ico image_viewer.py 2>nul
if not exist "icon.ico" (
    pyinstaller --onefile --windowed --name="图片查看器-GUI版" image_viewer.py
)

if errorlevel 1 (
    echo 错误: GUI版本打包失败
    pause
    exit /b 1
)

echo GUI版本打包成功！

echo.
echo 步骤4: 打包命令行版本...
echo ----------------------------------------

:: 打包命令行版本
echo 正在打包console_image_viewer.py (命令行版本)...
pyinstaller --onefile --console --name="图片查看器-命令行版" console_image_viewer.py

if errorlevel 1 (
    echo 错误: 命令行版本打包失败
    pause
    exit /b 1
)

echo 命令行版本打包成功！

echo.
echo 步骤5: 整理输出文件...
echo ----------------------------------------

:: 创建输出目录
if not exist "release" mkdir "release"

:: 复制exe文件到release目录
if exist "dist\图片查看器-GUI版.exe" (
    copy "dist\图片查看器-GUI版.exe" "release\"
    echo 已复制: 图片查看器-GUI版.exe
)

if exist "dist\图片查看器-命令行版.exe" (
    copy "dist\图片查看器-命令行版.exe" "release\"
    echo 已复制: 图片查看器-命令行版.exe
)

:: 复制说明文件
if exist "图片查看器说明.md" (
    copy "图片查看器说明.md" "release\"
    echo 已复制: 图片查看器说明.md
)

if exist "使用示例.md" (
    copy "使用示例.md" "release\"
    echo 已复制: 使用示例.md
)

:: 创建使用说明
echo 创建EXE使用说明...
(
echo # 图片查看器 EXE版本使用说明
echo.
echo ## 文件说明
echo.
echo - **图片查看器-GUI版.exe** - 图形界面版本
echo - **图片查看器-命令行版.exe** - 命令行版本
echo.
echo ## 使用方法
echo.
echo ### GUI版本
echo 1. 将exe文件放在包含barcode0812文件夹的目录中
echo 2. 双击运行"图片查看器-GUI版.exe"
echo 3. 使用键盘方向键或鼠标控制图片切换
echo.
echo ### 命令行版本
echo 1. 将exe文件放在包含barcode0812文件夹的目录中
echo 2. 双击运行"图片查看器-命令行版.exe"
echo 3. 按照提示使用命令控制
echo.
echo ## 控制说明
echo.
echo ### GUI版本控制
echo - 方向键 ←→ 或 A/D 键：切换图片
echo - 鼠标左键：下一张图片
echo - 鼠标右键：上一张图片
echo - ESC键：退出程序
echo.
echo ### 命令行版本控制
echo - n 或回车：下一张图片
echo - p：上一张图片
echo - j ^<数字^>：跳转到指定图片
echo - s：显示统计信息
echo - q：退出程序
echo.
echo ## 注意事项
echo.
echo 1. 确保barcode0812文件夹与exe文件在同一目录
echo 2. 只支持PNG格式的图片
echo 3. 如果遇到问题，请查看详细说明文档
echo.
echo ## 目录结构示例
echo.
echo ```
echo 运行目录/
echo ├── 图片查看器-GUI版.exe
echo ├── 图片查看器-命令行版.exe
echo └── barcode0812/
echo     ├── 文件夹1/
echo     │   ├── 图片1.png
echo     │   └── 图片2.png
echo     └── 文件夹2/
echo         └── 图片3.png
echo ```
) > "release\EXE使用说明.md"

echo 已创建: EXE使用说明.md

echo.
echo 步骤6: 清理临时文件...
echo ----------------------------------------

:: 清理构建文件
if exist "build" (
    rmdir /s /q "build"
    echo 已删除build目录
)

if exist "dist" (
    rmdir /s /q "dist"
    echo 已删除dist目录
)

if exist "*.spec" (
    del /q "*.spec"
    echo 已删除spec文件
)

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 输出文件位置: release\ 目录
echo.
echo 生成的文件:
if exist "release\图片查看器-GUI版.exe" echo   ✓ 图片查看器-GUI版.exe
if exist "release\图片查看器-命令行版.exe" echo   ✓ 图片查看器-命令行版.exe
if exist "release\EXE使用说明.md" echo   ✓ EXE使用说明.md
if exist "release\图片查看器说明.md" echo   ✓ 图片查看器说明.md
if exist "release\使用示例.md" echo   ✓ 使用示例.md

echo.
echo 使用方法:
echo 1. 将release目录中的exe文件复制到包含barcode0812文件夹的目录
echo 2. 双击运行对应的exe文件
echo.
echo 按任意键退出...
pause >nul
