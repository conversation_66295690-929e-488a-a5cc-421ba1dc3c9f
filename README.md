# 图片均匀显示器项目

## 项目简介

这是一个Python图片查看器项目，可以从多个文件夹中均匀地依次显示PNG图片。项目实现了智能的图片分配算法，确保不同文件夹的图片能够均匀穿插显示，避免某个文件夹的图片集中出现。

## 核心功能

- **均匀分配算法** - 自动计算每个文件夹的图片数量，按比例分配到多个轮次
- **随机打散** - 每轮内的图片随机排序，避免固定顺序
- **多种控制方式** - 支持键盘、鼠标、命令行操作
- **进度显示** - 实时显示浏览进度和总数
- **自适应缩放** - 图片自动缩放适应窗口大小

## 项目文件结构

```
image_viewer_project/
├── README.md                      # 项目说明（本文件）
├── 
├── # 核心程序文件
├── image_viewer.py                 # GUI版本图片查看器
├── console_image_viewer.py         # 命令行版本图片查看器
├── test_image_viewer.py            # 算法测试脚本
├── 
├── # 打包脚本
├── build_exe.bat                   # Windows完整自动化打包脚本
├── simple_build.bat               # Windows简化打包脚本
├── install_requirements.bat       # Windows依赖安装脚本
├── test_build.bat                 # Windows环境检查脚本
├── build_exe.sh                   # Linux打包脚本
├── 
├── # 配置文件
├── requirements.txt               # Python依赖包列表
├── 
├── # 说明文档
├── 图片查看器说明.md              # 详细功能说明
├── 使用示例.md                    # 使用示例和项目概述
├── 打包说明.md                    # 详细打包说明
├── EXE打包完整指南.md             # 完整打包指南
├── 
└── # 示例数据
    └── barcode0812/               # 示例图片文件夹
        ├── 文件夹1/
        │   ├── 图片1.png
        │   └── 图片2.png
        └── 文件夹2/
            └── 图片3.png
```

## 快速开始

### 1. 运行Python版本

```bash
# 安装依赖
pip install Pillow

# 运行GUI版本
python image_viewer.py

# 运行命令行版本
python console_image_viewer.py

# 测试算法
python test_image_viewer.py
```

### 2. 打包成EXE（Windows）

```batch
# 一键打包（推荐）
build_exe.bat

# 或者分步打包
install_requirements.bat
simple_build.bat
```

### 3. 打包成可执行文件（Linux）

```bash
# 给脚本执行权限
chmod +x build_exe.sh

# 运行打包脚本
./build_exe.sh
```

## 使用说明

### GUI版本控制
- `→` 或 `D` 键：下一张图片
- `←` 或 `A` 键：上一张图片
- 鼠标左键：下一张图片
- 鼠标右键：上一张图片
- `ESC` 键：退出程序

### 命令行版本控制
- `n` 或回车：下一张图片
- `p`：上一张图片
- `j <数字>`：跳转到指定图片
- `s`：显示统计信息
- `q`：退出程序

## 算法原理

假设有3个文件夹：
- 文件夹A：9张图片
- 文件夹B：6张图片
- 文件夹C：3张图片

程序会：
1. 以最大图片数（9）作为轮次数
2. 每轮从各文件夹中按比例取出图片
3. 每轮内的图片随机打散
4. 确保不同文件夹的图片均匀穿插显示

## 系统要求

- Python 3.6+
- Pillow库
- tkinter（通常随Python一起安装）
- Windows 7+ 或 Linux（用于打包）

## 依赖包

```
Pillow>=9.0.0
```

## 注意事项

1. 只支持PNG格式的图片
2. 确保图片文件夹与程序在同一目录
3. GUI版本需要图形界面环境
4. 打包后的exe文件较大（50-100MB）是正常现象

## 常见问题

### Q: 找不到barcode0812目录
A: 确保程序文件与barcode0812文件夹在同一目录

### Q: 图片无法显示
A: 检查图片文件是否损坏，确保是PNG格式

### Q: GUI无法启动
A: 在无图形界面环境中使用命令行版本

### Q: 打包失败
A: 检查Python版本，确保网络连接正常

## 技术特点

- **单一职责原则** - 每个模块功能明确
- **开闭原则** - 易于扩展新功能
- **简洁设计** - 避免过度工程化
- **跨平台兼容** - 支持Windows和Linux
- **用户友好** - 提供GUI和命令行两种界面

## 许可证

本项目为开源项目，可自由使用和修改。

## 更新日志

- v1.0 - 初始版本，实现基本功能
- v1.1 - 添加命令行版本
- v1.2 - 添加打包脚本和完整文档

## 联系方式

如有问题或建议，请查看项目文档或提交issue。
