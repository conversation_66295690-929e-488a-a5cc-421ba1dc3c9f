#!/usr/bin/env python3
"""
简化版图片查看器测试脚本
用于测试基本功能
"""

import os
import random
import math

def test_image_loading():
    """测试图片加载和分配算法"""
    root_dir = "barcode0812"
    
    if not os.path.exists(root_dir):
        print(f"错误: 找不到 {root_dir} 目录")
        return
    
    # 获取所有文件夹及其图片数量
    folders = []
    for item in os.listdir(root_dir):
        folder_path = os.path.join(root_dir, item)
        if os.path.isdir(folder_path):
            images = [f for f in os.listdir(folder_path) 
                     if f.lower().endswith('.png')]
            if images:
                folders.append({
                    'name': item,
                    'path': folder_path,
                    'images': sorted(images),
                    'count': len(images)
                })
    
    if not folders:
        print("没有找到包含PNG图片的文件夹")
        return
    
    # 按图片数量排序（从多到少）
    folders.sort(key=lambda x: x['count'], reverse=True)
    
    print(f"找到 {len(folders)} 个文件夹:")
    for i, folder in enumerate(folders[:10]):  # 只显示前10个
        print(f"  {i+1}. {folder['name']}: {folder['count']} 张图片")
    
    if len(folders) > 10:
        print(f"  ... 还有 {len(folders) - 10} 个文件夹")
    
    # 计算总图片数
    total_images = sum(folder['count'] for folder in folders)
    max_images = folders[0]['count']
    
    print(f"\n统计信息:")
    print(f"  总文件夹数: {len(folders)}")
    print(f"  总图片数: {total_images}")
    print(f"  最多图片的文件夹: {folders[0]['name']} ({max_images} 张)")
    print(f"  最少图片的文件夹: {folders[-1]['name']} ({folders[-1]['count']} 张)")
    
    # 测试均匀分配算法
    print(f"\n测试均匀分配算法:")
    print(f"  将按 {max_images} 轮进行分配")
    
    # 为每个文件夹计算每轮应该分配的图片数
    for folder in folders:
        folder['per_round'] = max(1, folder['count'] // max_images)
        folder['remaining'] = folder['count']
        folder['current_images'] = folder['images'].copy()
        # 随机打散每个文件夹的图片
        random.shuffle(folder['current_images'])
    
    # 按轮次分配图片（只测试前3轮）
    image_list = []
    test_rounds = min(3, max_images)
    
    for round_num in range(test_rounds):
        round_images = []
        print(f"\n  第 {round_num + 1} 轮分配:")
        
        for folder in folders:
            if folder['remaining'] > 0:
                # 计算这一轮应该取多少张图片
                remaining_rounds = max_images - round_num
                images_this_round = min(
                    folder['per_round'],
                    folder['remaining'],
                    math.ceil(folder['remaining'] / remaining_rounds)
                )
                
                # 取出这一轮的图片
                round_folder_images = []
                for _ in range(images_this_round):
                    if folder['current_images']:
                        img_name = folder['current_images'].pop(0)
                        img_path = os.path.join(folder['path'], img_name)
                        round_folder_images.append({
                            'path': img_path,
                            'folder': folder['name'],
                            'filename': img_name
                        })
                        folder['remaining'] -= 1
                
                if round_folder_images:
                    round_images.extend(round_folder_images)
                    print(f"    {folder['name']}: {len(round_folder_images)} 张")
        
        # 随机打散这一轮的图片
        random.shuffle(round_images)
        image_list.extend(round_images)
        print(f"    本轮总计: {len(round_images)} 张")
    
    print(f"\n测试结果:")
    print(f"  前 {test_rounds} 轮总计: {len(image_list)} 张图片")
    
    # 显示前几张图片的信息
    print(f"\n前10张图片的分配情况:")
    for i, img in enumerate(image_list[:10]):
        print(f"  {i+1}. {img['folder']} - {img['filename']}")
    
    print(f"\n算法测试完成！")

if __name__ == "__main__":
    test_image_loading()
