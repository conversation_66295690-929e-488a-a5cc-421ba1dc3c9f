# 图片查看器 EXE 打包说明

## 概述

本文档说明如何将Python图片查看器脚本打包成独立的EXE可执行文件。

## 准备工作

### 1. 确保Python环境

- Python 3.6 或更高版本
- pip 包管理器

### 2. 检查必要文件

确保以下文件存在：
- `image_viewer.py` - GUI版本脚本
- `console_image_viewer.py` - 命令行版本脚本

## 打包步骤

### 方法一：使用完整打包脚本（推荐）

1. **运行环境检查**
   ```batch
   test_build.bat
   ```
   这会检查Python环境和必要文件是否齐全。

2. **安装依赖包**
   ```batch
   install_requirements.bat
   ```
   或者手动安装：
   ```batch
   pip install Pillow pyinstaller
   ```

3. **执行打包**
   ```batch
   build_exe.bat
   ```
   这个脚本会：
   - 自动安装依赖包
   - 清理旧的构建文件
   - 打包GUI版本和命令行版本
   - 整理输出文件到release目录
   - 创建使用说明

### 方法二：使用简单打包脚本

如果完整脚本有问题，可以使用简化版本：

```batch
simple_build.bat
```

### 方法三：手动打包

1. **安装PyInstaller**
   ```batch
   pip install pyinstaller
   ```

2. **打包GUI版本**
   ```batch
   pyinstaller --onefile --windowed image_viewer.py
   ```

3. **打包命令行版本**
   ```batch
   pyinstaller --onefile --console console_image_viewer.py
   ```

## 打包参数说明

### PyInstaller 常用参数

- `--onefile` - 打包成单个exe文件
- `--windowed` - 不显示控制台窗口（适合GUI程序）
- `--console` - 显示控制台窗口（适合命令行程序）
- `--name` - 指定输出文件名
- `--icon` - 指定程序图标

### 示例命令

```batch
# GUI版本（无控制台窗口）
pyinstaller --onefile --windowed --name="图片查看器" image_viewer.py

# 命令行版本（有控制台窗口）
pyinstaller --onefile --console --name="图片查看器-控制台" console_image_viewer.py

# 带图标的版本
pyinstaller --onefile --windowed --icon=icon.ico image_viewer.py
```

## 输出文件

打包完成后，会在以下位置生成文件：

### 使用完整脚本
```
release/
├── 图片查看器-GUI版.exe
├── 图片查看器-命令行版.exe
├── EXE使用说明.md
├── 图片查看器说明.md
└── 使用示例.md
```

### 使用简单脚本或手动打包
```
dist/
├── image_viewer.exe
└── console_image_viewer.exe
```

## 使用打包后的EXE

### 1. 部署要求

将EXE文件放在包含`barcode0812`文件夹的目录中：

```
运行目录/
├── 图片查看器-GUI版.exe
├── 图片查看器-命令行版.exe
└── barcode0812/
    ├── 文件夹1/
    │   ├── 图片1.png
    │   └── 图片2.png
    └── 文件夹2/
        └── 图片3.png
```

### 2. 运行方式

- **GUI版本**：双击`图片查看器-GUI版.exe`
- **命令行版本**：双击`图片查看器-命令行版.exe`

## 常见问题

### 1. 打包失败

**问题**：PyInstaller安装失败
**解决**：
```batch
pip install --upgrade pip
pip install pyinstaller
```

**问题**：缺少模块错误
**解决**：
```batch
pip install Pillow
```

### 2. EXE运行失败

**问题**：找不到barcode0812目录
**解决**：确保EXE文件与barcode0812文件夹在同一目录

**问题**：EXE文件过大
**解决**：这是正常的，PyInstaller会打包Python解释器和所有依赖

### 3. 杀毒软件误报

**问题**：杀毒软件报告EXE文件有病毒
**解决**：这是误报，可以添加到白名单或使用代码签名

## 优化建议

### 1. 减小文件大小

```batch
# 使用UPX压缩（需要先安装UPX）
pyinstaller --onefile --windowed --upx-dir=upx image_viewer.py
```

### 2. 添加图标

1. 准备一个`.ico`格式的图标文件
2. 使用`--icon`参数：
   ```batch
   pyinstaller --onefile --windowed --icon=icon.ico image_viewer.py
   ```

### 3. 隐藏导入

如果遇到模块导入问题，可以使用：
```batch
pyinstaller --onefile --windowed --hidden-import=PIL image_viewer.py
```

## 分发说明

### 1. 单机分发

直接复制EXE文件和barcode0812文件夹到目标机器。

### 2. 网络分发

可以将EXE文件上传到网盘或服务器，用户下载后配合自己的图片文件夹使用。

### 3. 安装包制作

可以使用NSIS、Inno Setup等工具制作安装包，自动部署EXE文件和必要的文件夹结构。

## 注意事项

1. **文件大小**：打包后的EXE文件会比较大（通常50-100MB），这是正常的
2. **启动速度**：首次启动可能较慢，这是因为需要解压内置的Python环境
3. **兼容性**：在Windows 7及以上系统中运行良好
4. **依赖**：EXE文件是独立的，不需要目标机器安装Python

## 脚本文件说明

- `build_exe.bat` - 完整的自动化打包脚本
- `simple_build.bat` - 简化的打包脚本
- `install_requirements.bat` - 依赖包安装脚本
- `test_build.bat` - 环境检查脚本
