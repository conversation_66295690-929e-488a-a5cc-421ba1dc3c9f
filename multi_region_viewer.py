#!/usr/bin/env python
"""
多区域同时显示图片查看器
九宫格布局同时显示所有区域的图片
支持单区域临时浏览模式
"""

import os
import random
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import math
from pathlib import Path

class RegionPanel:
    """单个区域的显示面板"""
    def __init__(self, parent, region_code, images, viewer_callback):
        self.region_code = region_code
        self.images = images
        self.current_index = 0
        self.viewer_callback = viewer_callback
        
        # 创建区域框架
        self.frame = tk.Frame(parent, bg='darkgray', relief=tk.RAISED, bd=2)
        
        # 区域标题
        self.title_label = tk.Label(
            self.frame,
            text=f"区域 {region_code}",
            font=('Arial', 12, 'bold'),
            bg='darkblue',
            fg='white'
        )
        self.title_label.pack(fill=tk.X)
        
        # 图片显示区域
        self.image_frame = tk.Frame(self.frame, bg='black', width=300, height=200)
        self.image_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        self.image_frame.pack_propagate(False)  # 保持固定大小
        
        self.image_label = tk.Label(self.image_frame, bg='black')
        self.image_label.pack(expand=True)
        
        # 控制按钮框架
        button_frame = tk.Frame(self.frame, bg='darkgray')
        button_frame.pack(fill=tk.X, padx=2, pady=2)
        
        # 上一张按钮
        self.prev_btn = tk.Button(
            button_frame,
            text="◀",
            command=self.prev_image,
            font=('Arial', 10, 'bold'),
            bg='gray',
            fg='white',
            width=3
        )
        self.prev_btn.pack(side=tk.LEFT, padx=(0, 2))
        
        # 进度标签
        self.progress_label = tk.Label(
            button_frame,
            text="0/0",
            font=('Arial', 9),
            bg='darkgray',
            fg='white'
        )
        self.progress_label.pack(side=tk.LEFT, expand=True)
        
        # 下一张按钮
        self.next_btn = tk.Button(
            button_frame,
            text="▶",
            command=self.next_image,
            font=('Arial', 10, 'bold'),
            bg='gray',
            fg='white',
            width=3
        )
        self.next_btn.pack(side=tk.RIGHT, padx=(2, 0))
        
        # 文件信息标签
        self.info_label = tk.Label(
            self.frame,
            text="",
            font=('Arial', 8),
            bg='darkgray',
            fg='lightgray',
            wraplength=280
        )
        self.info_label.pack(fill=tk.X, padx=2, pady=(0, 2))
        
        # 显示第一张图片
        self.show_image()
    
    def show_image(self):
        """显示当前图片"""
        if not self.images:
            self.image_label.configure(image='', text="无图片")
            self.progress_label.config(text="0/0")
            self.info_label.config(text="")
            return
        
        # 确保索引在有效范围内
        if self.current_index >= len(self.images):
            self.current_index = 0
        elif self.current_index < 0:
            self.current_index = len(self.images) - 1
        
        current_image = self.images[self.current_index]
        
        try:
            # 加载图片
            image = Image.open(current_image['path'])
            
            # 计算缩放比例，保持宽高比
            img_width, img_height = image.size
            max_width, max_height = 280, 180
            
            scale_w = max_width / img_width
            scale_h = max_height / img_height
            scale = min(scale_w, scale_h, 1.0)  # 不放大，只缩小
            
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            
            # 调整图片大小
            if scale < 1.0:
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter可用的格式
            photo = ImageTk.PhotoImage(image)
            
            # 显示图片
            self.image_label.configure(image=photo, text='')
            self.image_label.image = photo  # 保持引用
            
            # 更新进度和信息
            self.progress_label.config(text=f"{self.current_index + 1}/{len(self.images)}")
            folder_name = current_image['folder']
            filename = current_image['filename']
            self.info_label.config(text=f"{folder_name}\n{filename}")
            
        except Exception as e:
            print(f"无法加载图片 {current_image['path']}: {e}")
            self.image_label.configure(image='', text="加载失败")
    
    def next_image(self):
        """下一张图片"""
        if not self.images:
            return
        
        # 通知主查看器进入单区域模式
        self.viewer_callback('single_mode', self.region_code)
        
        self.current_index = (self.current_index + 1) % len(self.images)
        self.show_image()
    
    def prev_image(self):
        """上一张图片"""
        if not self.images:
            return
        
        # 通知主查看器进入单区域模式
        self.viewer_callback('single_mode', self.region_code)
        
        self.current_index = (self.current_index - 1) % len(self.images)
        self.show_image()
    
    def set_highlight(self, highlight):
        """设置高亮状态"""
        if highlight:
            self.frame.config(bg='orange', bd=4)
            self.title_label.config(bg='darkorange')
        else:
            self.frame.config(bg='darkgray', bd=2)
            self.title_label.config(bg='darkblue')

class MultiRegionViewer:
    def __init__(self, root_dir="barcode"):
        self.root_dir = root_dir
        self.regions = {}  # 区域 -> 图片列表的映射
        self.region_panels = {}  # 区域 -> RegionPanel的映射
        self.current_single_region = None  # 当前单区域模式的区域
        self.single_mode = False
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("多区域图片显示器")
        self.root.geometry("1200x800")
        self.root.configure(bg='black')
        
        # 设置窗口居中
        self.center_window()
        
        # 加载图片数据
        self.load_images_by_region()
        
        # 创建界面
        self.setup_ui()
        
        # 绑定事件
        self.bind_events()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def extract_region_from_folder(self, folder_name):
        """从文件夹名称中提取区域代码"""
        if '-' not in folder_name:
            return None
        
        after_dash = folder_name.split('-')[1]
        if len(after_dash) < 3:
            return None
        
        three_chars = after_dash[:3]
        if three_chars[0] not in ['Z', 'P', 'X']:
            return None
        
        region_digits = three_chars[1:]
        if not region_digits.isdigit():
            return None
        
        return region_digits

    def load_images_by_region(self):
        """按区域加载图片"""
        if not os.path.exists(self.root_dir):
            print(f"目录 {self.root_dir} 不存在")
            return

        # 获取所有文件夹及其区域
        region_folders = {}

        for item in os.listdir(self.root_dir):
            folder_path = os.path.join(self.root_dir, item)
            if os.path.isdir(folder_path):
                region = self.extract_region_from_folder(item)
                if region:
                    if region not in region_folders:
                        region_folders[region] = []

                    # 获取该文件夹中的图片
                    images = [f for f in os.listdir(folder_path)
                             if f.lower().endswith('.png')]

                    if images:
                        for img in sorted(images):
                            img_path = os.path.join(folder_path, img)
                            region_folders[region].append({
                                'path': img_path,
                                'folder': item,
                                'filename': img,
                                'region': region
                            })

        # 对每个区域的图片进行均匀分配
        self.regions = {}
        for region, images in region_folders.items():
            self.regions[region] = self.distribute_images_evenly(images)

        print(f"找到 {len(self.regions)} 个区域:")
        for region in sorted(self.regions.keys()):
            print(f"  区域 {region}: {len(self.regions[region])} 张图片")

    def distribute_images_evenly(self, images):
        """均匀分配图片"""
        if not images:
            return []

        # 按文件夹分组
        folder_groups = {}
        for img in images:
            folder = img['folder']
            if folder not in folder_groups:
                folder_groups[folder] = []
            folder_groups[folder].append(img)

        # 按图片数量排序（从多到少）
        folders = []
        for folder, imgs in folder_groups.items():
            folders.append({
                'name': folder,
                'images': imgs,
                'count': len(imgs)
            })

        folders.sort(key=lambda x: x['count'], reverse=True)

        if not folders:
            return []

        # 计算轮次数（以最多图片的文件夹为准）
        max_images = folders[0]['count']
        rounds = max_images

        # 为每个文件夹计算每轮应该分配的图片数
        for folder in folders:
            folder['per_round'] = max(1, folder['count'] // rounds)
            folder['remaining'] = folder['count']
            folder['current_images'] = folder['images'].copy()
            # 随机打散每个文件夹的图片
            random.shuffle(folder['current_images'])

        # 按轮次分配图片
        distributed_images = []

        for round_num in range(rounds):
            round_images = []

            for folder in folders:
                if folder['remaining'] > 0:
                    # 计算这一轮应该取多少张图片
                    remaining_rounds = rounds - round_num
                    images_this_round = min(
                        folder['per_round'],
                        folder['remaining'],
                        math.ceil(folder['remaining'] / remaining_rounds)
                    )

                    # 取出这一轮的图片
                    for _ in range(images_this_round):
                        if folder['current_images']:
                            img = folder['current_images'].pop(0)
                            round_images.append(img)
                            folder['remaining'] -= 1

            # 随机打散这一轮的图片
            random.shuffle(round_images)
            distributed_images.extend(round_images)

        return distributed_images

    def setup_ui(self):
        """设置用户界面"""
        # 顶部控制栏
        control_frame = tk.Frame(self.root, bg='black')
        control_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        # 标题
        title_label = tk.Label(
            control_frame,
            text="多区域图片显示器",
            font=('Arial', 16, 'bold'),
            bg='black',
            fg='white'
        )
        title_label.pack(side=tk.LEFT)

        # 模式显示
        self.mode_label = tk.Label(
            control_frame,
            text="多区域模式",
            font=('Arial', 12, 'bold'),
            bg='black',
            fg='lightgreen'
        )
        self.mode_label.pack(side=tk.LEFT, padx=(20, 0))

        # 返回按钮
        self.back_button = tk.Button(
            control_frame,
            text="返回多区域模式",
            command=self.back_to_multi_mode,
            font=('Arial', 12),
            bg='darkred',
            fg='white',
            state='disabled'
        )
        self.back_button.pack(side=tk.RIGHT)

        # 主显示区域
        self.main_frame = tk.Frame(self.root, bg='black')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建区域面板
        self.create_region_panels()

    def create_region_panels(self):
        """创建区域面板"""
        regions = sorted(self.regions.keys())

        # 计算网格布局
        num_regions = len(regions)
        if num_regions <= 4:
            cols = 2
            rows = 2
        elif num_regions <= 6:
            cols = 3
            rows = 2
        elif num_regions <= 9:
            cols = 3
            rows = 3
        else:
            cols = 4
            rows = math.ceil(num_regions / cols)

        # 创建区域面板
        for i, region in enumerate(regions):
            row = i // cols
            col = i % cols

            panel = RegionPanel(
                self.main_frame,
                region,
                self.regions[region],
                self.on_panel_action
            )

            panel.frame.grid(row=row, column=col, padx=5, pady=5, sticky='nsew')
            self.region_panels[region] = panel

        # 配置网格权重
        for i in range(rows):
            self.main_frame.grid_rowconfigure(i, weight=1)
        for i in range(cols):
            self.main_frame.grid_columnconfigure(i, weight=1)

    def on_panel_action(self, action, region):
        """处理面板动作"""
        if action == 'single_mode':
            self.enter_single_mode(region)

    def enter_single_mode(self, region):
        """进入单区域模式"""
        self.single_mode = True
        self.current_single_region = region

        # 更新界面状态
        self.mode_label.config(text=f"单区域模式 - 区域 {region}", fg='orange')
        self.back_button.config(state='normal')

        # 隐藏其他区域面板，高亮当前区域
        for r, panel in self.region_panels.items():
            if r == region:
                panel.set_highlight(True)
            else:
                panel.frame.grid_remove()  # 隐藏面板

        # 重新布局当前区域面板到中央
        current_panel = self.region_panels[region]
        current_panel.frame.grid(row=0, column=0, padx=20, pady=20, sticky='nsew')

        # 调整网格权重
        self.main_frame.grid_rowconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)

        # 清除其他行列的权重
        for i in range(1, 10):  # 清除可能的其他行列
            self.main_frame.grid_rowconfigure(i, weight=0)
            self.main_frame.grid_columnconfigure(i, weight=0)

    def back_to_multi_mode(self):
        """返回多区域模式"""
        self.single_mode = False
        self.current_single_region = None

        # 更新界面状态
        self.mode_label.config(text="多区域模式", fg='lightgreen')
        self.back_button.config(state='disabled')

        # 清除当前布局
        for panel in self.region_panels.values():
            panel.frame.grid_remove()
            panel.set_highlight(False)

        # 重新创建多区域布局
        self.create_region_panels()

    def bind_events(self):
        """绑定事件"""
        self.root.bind('<Key>', self.on_key_press)
        self.root.focus_set()

    def on_key_press(self, event):
        """处理键盘事件"""
        key = event.keysym.lower()

        if key == 'escape':
            if self.single_mode:
                self.back_to_multi_mode()
            else:
                self.root.quit()
        elif key in ['1', '2', '3', '4', '5', '6', '7', '8', '9']:
            # 数字键快速切换到对应区域
            self.quick_switch_region(key)

    def quick_switch_region(self, key):
        """快速切换到指定区域"""
        regions = sorted(self.regions.keys())
        try:
            index = int(key) - 1
            if 0 <= index < len(regions):
                region = regions[index]
                self.enter_single_mode(region)
        except (ValueError, IndexError):
            pass

    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    """主函数"""
    print("多区域图片显示器启动中...")

    # 检查目录是否存在
    if not os.path.exists("barcode"):
        print("错误: 找不到 barcode 目录")
        print("请确保脚本在包含 barcode 文件夹的目录中运行")
        return

    # 创建并运行图片查看器
    viewer = MultiRegionViewer()

    print("\n使用说明:")
    print("- 九宫格同时显示所有区域的图片")
    print("- 点击任意区域的上一张/下一张按钮进入单区域模式")
    print("- 在单区域模式下点击'返回多区域模式'按钮返回")
    print("- 键盘控制: ESC(返回多区域模式或退出)")
    print("- 数字键1-9: 快速切换到对应区域的单区域模式")
    print("- 每个区域内的图片会均匀打散显示")

    viewer.run()

if __name__ == "__main__":
    main()
